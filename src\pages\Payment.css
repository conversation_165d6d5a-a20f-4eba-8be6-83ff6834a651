/* Payment Page Styles */
.payment-page {
  min-height: 100vh;
  background-color: var(--light-gray);
  display: flex;
}

/* Sidebar Styles */
.sidebar {
  width: 250px;
  background-color: var(--primary-color);
  color: white;
  height: 100vh;
  position: fixed;
  padding: 20px 0;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.sidebar-header {
  text-align: center;
  padding: 0 20px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.school-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 15px;
  border-radius: 50%;
  border: 3px solid var(--secondary-color);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
}

.school-logo img {
  width: 90%;
  height: 90%;
  object-fit: contain;
}

.sidebar-header h2 {
  color: var(--secondary-color);
  margin-bottom: 5px;
  font-size: 18px;
}

.sidebar-menu {
  margin-top: 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 15px 25px;
  color: white;
  text-decoration: none;
  transition: all 0.3s;
}

.menu-item:hover {
  background-color: var(--primary-dark);
  color: white;
  text-decoration: none;
}

.menu-item.active {
  background-color: var(--primary-dark);
  border-left: 4px solid var(--secondary-color);
}

.menu-item i {
  margin-right: 10px;
  color: var(--secondary-light);
  min-width: 20px;
  text-align: center;
  font-size: 18px;
}

.menu-text {
  white-space: nowrap;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 250px;
  padding: 20px;
  transition: margin-left 0.3s ease;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  background-color: var(--primary-color);
  color: white;
  padding: 30px;
  text-align: center;
  border-radius: 8px;
  margin-bottom: 30px;
  box-shadow: var(--shadow-sm);
}

.page-header h1 {
  font-size: 28px;
  margin-bottom: 10px;
  color: white;
}

.student-info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  box-shadow: var(--shadow-sm);
}

.student-info h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

/* Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40px;
  position: relative;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
}

.progress-bar {
  position: absolute;
  top: 50%;
  left: 0;
  height: 4px;
  background: var(--gradient-primary);
  transition: width 0.3s ease;
  z-index: 1;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  z-index: 2;
  background: white;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.progress-step:hover {
  transform: translateY(-2px);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--medium-gray);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.progress-step.active .step-number {
  background: var(--primary-color);
}

.step-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--dark-gray);
  text-align: center;
}

.progress-step.active .step-title {
  color: var(--primary-color);
  font-weight: 600;
}

/* Payment Cards */
.payment-sections {
  margin-top: 30px;
}

.payment-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s ease;
}

.payment-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.payment-card.locked {
  opacity: 0.6;
  border-left-color: var(--medium-gray);
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--medium-gray);
}

.payment-header h3 {
  color: var(--primary-color);
  margin: 0;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-uploaded {
  background: #d1ecf1;
  color: #0c5460;
}

.status-verified {
  background: #d4edda;
  color: #155724;
}

.status-locked {
  background: #f8d7da;
  color: #721c24;
}

.fee-amount {
  text-align: center;
  margin: 20px 0;
}

.amount {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
}

.bank-details {
  margin: 25px 0;
}

.bank-details h4 {
  color: var(--primary-color);
  margin-bottom: 15px;
}

.bank-info {
  background: var(--light-gray);
  padding: 20px;
  border-radius: 8px;
}

.bank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--medium-gray);
}

.bank-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.bank-label {
  font-weight: 600;
  color: var(--dark-gray);
}

.bank-value {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-dark);
}

.copy-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  background: var(--primary-dark);
}

.payment-instructions {
  margin: 25px 0;
}

.payment-instructions h4 {
  color: var(--primary-color);
  margin-bottom: 15px;
}

.payment-instructions ol {
  padding-left: 20px;
}

.payment-instructions li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.receipt-upload {
  margin: 25px 0;
}

.receipt-upload h4 {
  color: var(--primary-color);
  margin-bottom: 15px;
}

.upload-section {
  text-align: center;
  padding: 20px;
  border: 2px dashed var(--medium-gray);
  border-radius: 8px;
  background: var(--light-gray);
}

.upload-btn {
  display: inline-block;
  background: var(--gradient-primary);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: white;
  text-decoration: none;
}

.upload-note {
  margin-top: 10px;
  font-size: 14px;
  color: var(--dark-gray);
}

.upload-success {
  text-align: center;
  padding: 20px;
  background: #d4edda;
  color: #155724;
  border-radius: 8px;
  margin: 20px 0;
}

.upload-success i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

.locked-message {
  text-align: center;
  padding: 40px;
  color: var(--dark-gray);
}

.locked-message i {
  font-size: 48px;
  margin-bottom: 15px;
  color: var(--medium-gray);
}

/* Mobile Toggle */
.sidebar-toggle {
  display: none;
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 1100;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    padding: 15px;
  }

  .sidebar-toggle {
    display: block;
  }

  .progress-container {
    flex-direction: column;
    gap: 20px;
  }

  .progress-bar {
    display: none;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .bank-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .payment-card {
    padding: 20px;
  }
}
