import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Header.css';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const isActive = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-light fixed-top">
      <div className="container">
        <Link className="navbar-brand d-flex align-items-center" to="/">
          <img src="/img/CUAB.jpg" alt="CUAB Logo" className="me-2" />
          CUAB PG Portal
        </Link>
        
        <button 
          className="navbar-toggler" 
          type="button" 
          onClick={toggleMenu}
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>
        
        <div className={`collapse navbar-collapse ${isMenuOpen ? 'show' : ''}`} id="navbarNav">
          <ul className="navbar-nav ms-auto align-items-center">
            <li className="nav-item">
              <Link className={`nav-link ${isActive('/')}`} to="/">Home</Link>
            </li>
            <li className="nav-item">
              <Link className={`nav-link ${isActive('/about')}`} to="/about">About Us</Link>
            </li>
            <li className="nav-item dropdown">
              <a 
                className="nav-link dropdown-toggle" 
                href="#" 
                id="collegesDropdown" 
                role="button" 
                data-bs-toggle="dropdown" 
                aria-expanded="false"
              >
                Colleges
              </a>
              <ul className="dropdown-menu" aria-labelledby="collegesDropdown">
                <li><a className="dropdown-item" href="/colleges/abs">Abeokuta Business School (ABS)</a></li>
                <li><a className="dropdown-item" href="/colleges/bacolaw">Bola Ajibola College of Law (BACOLAW)</a></li>
                <li><a className="dropdown-item" href="/colleges/casmas">College of Arts, Social and Management Sciences (CASMAS)</a></li>
                <li><a className="dropdown-item" href="/colleges/coes">College of Environmental Sciences (COES)</a></li>
                <li><a className="dropdown-item" href="/colleges/cohes">College of Health Sciences (COHES)</a></li>
                <li><a className="dropdown-item" href="/colleges/cicot">College of Information and Communication Technology (CICOT)</a></li>
                <li><a className="dropdown-item" href="/colleges/conas">College of Natural and Applied Sciences (CONAS)</a></li>
              </ul>
            </li>
            <li className="nav-item">
              <Link className={`nav-link ${isActive('/programmes')}`} to="/programmes">Programmes</Link>
            </li>
            <li className="nav-item">
              <Link className={`nav-link ${isActive('/apply')}`} to="/apply">Apply Now</Link>
            </li>
            <li className="nav-item">
              <Link className={`nav-link btn-login ${isActive('/login')}`} to="/login">Login</Link>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
};

export default Header;
