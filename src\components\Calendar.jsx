import React, { useState } from 'react';
import './Calendar.css';

const Calendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date(2025, 5, 1)); // June 2025

  const monthNames = ["January", "February", "March", "April", "May", "June", 
                     "July", "August", "September", "October", "November", "December"];
  const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const generateCalendarDays = () => {
    const month = currentDate.getMonth();
    const year = currentDate.getFullYear();
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const daysInPrevMonth = new Date(year, month, 0).getDate();
    
    const days = [];
    
    // Previous month days
    for (let i = 0; i < firstDay; i++) {
      days.push({
        day: daysInPrevMonth - firstDay + i + 1,
        isOtherMonth: true,
        isToday: false,
        hasEvent: false
      });
    }
    
    // Current month days
    for (let i = 1; i <= daysInMonth; i++) {
      const today = new Date();
      const isToday = i === today.getDate() && month === today.getMonth() && year === today.getFullYear();
      const hasEvent = Math.random() > 0.7; // Random events for demo
      
      days.push({
        day: i,
        isOtherMonth: false,
        isToday,
        hasEvent
      });
    }
    
    // Next month days
    const totalDaysRendered = firstDay + daysInMonth;
    const nextMonthDays = totalDaysRendered <= 35 ? 35 - totalDaysRendered : 42 - totalDaysRendered;
    
    for (let i = 1; i <= nextMonthDays; i++) {
      days.push({
        day: i,
        isOtherMonth: true,
        isToday: false,
        hasEvent: false
      });
    }
    
    return days;
  };

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const calendarDays = generateCalendarDays();

  return (
    <div className="calendar">
      <div className="calendar-header">
        <h4>{monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}</h4>
        <div className="calendar-nav">
          <button onClick={() => navigateMonth('prev')}>
            <i className="fas fa-chevron-left"></i>
          </button>
          <button onClick={() => navigateMonth('next')}>
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
      <div className="calendar-grid">
        {dayNames.map((day, index) => (
          <div key={index} className="calendar-day-header">{day}</div>
        ))}
        {calendarDays.map((day, index) => (
          <div 
            key={index} 
            className={`calendar-day ${day.isOtherMonth ? 'other-month' : ''} ${day.isToday ? 'today' : ''} ${day.hasEvent ? 'event' : ''}`}
          >
            {day.day}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Calendar;
