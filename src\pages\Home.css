/* Home Page Specific Styles */
.home-page {
  min-height: 100vh;
}

.hero {
  background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('/img/Cuabaud.jpeg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding: 6rem 0 4rem;
  min-height: 80vh;
  display: flex;
  align-items: center;
  animation: heroBackgroundPan 20s ease-in-out infinite alternate;
}

.hero .container {
  position: relative;
  z-index: 2;
}

.hero h1 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: var(--text-light);
}

.hero .lead {
  font-size: 1.25rem;
  color: var(--text-light);
  margin-bottom: 2rem;
  font-weight: 800;
  letter-spacing: 0.5px;
}

.btn-success {
  background: var(--gradient-primary);
  border: none;
  font-weight: 600;
  padding: 0.8rem 2rem;
  border-radius: 8px;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.campus-image {
  height: 300px;
  width: 100%;
  object-fit: cover;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cta-section {
  background: var(--gradient-primary);
  color: var(--text-light);
  padding: 4rem 0;
  margin-top: 3rem;
}

.cta-section h3 {
  color: var(--text-light);
  font-weight: 700;
}

.cta-section .btn-light {
  background: white;
  color: var(--primary-color);
  border: none;
  font-weight: 600;
  padding: 0.8rem 2rem;
  border-radius: 8px;
  transition: var(--transition);
}

.cta-section .btn-light:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

/* Keyframes for the background pan animation */
@keyframes heroBackgroundPan {
  0% {
    background-position: 40% center;
  }
  100% {
    background-position: 60% center;
  }
}

/* Responsive Design */
@media (max-width: 991px) {
  .hero h1 {
    font-size: 2rem;
  }
  .hero .lead {
    font-size: 1.1rem;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 5rem 0 3rem;
  }
  .hero h1 {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .hero h1 {
    font-size: 1.5rem;
  }
  .btn-success {
    width: 100%;
  }
}
