import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import Footer from '../components/Footer';
import './Login.css';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [errors, setErrors] = useState({});
  const [alert, setAlert] = useState({ message: '', type: '', show: false });
  const [isLoading, setIsLoading] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is remembered
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
      const userData = JSON.parse(rememberedUser);
      setFormData(prev => ({
        ...prev,
        email: userData.email || '',
        rememberMe: true
      }));
    }
  }, []);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear specific error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Please enter your registered email.';
    } else if (!/\S+@\S+\.\S+/.test(formData.email) && !/^APP\d+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email or Application ID.';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Please enter your password.';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters long.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const showAlert = (message, type) => {
    setAlert({ message, type, show: true });
    setTimeout(() => {
      setAlert(prev => ({ ...prev, show: false }));
    }, 5000);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    setIsValidated(true);

    if (!validateForm()) {
      showAlert('Please fill in all fields correctly.', 'danger');
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Demo validation
      if (formData.email === '<EMAIL>' && formData.password === 'Demo@1234') {
        // Remember user if checkbox is checked
        if (formData.rememberMe) {
          localStorage.setItem('rememberedUser', JSON.stringify({
            email: formData.email
          }));
        }

        // Store user session
        localStorage.setItem('userSession', JSON.stringify({
          email: formData.email,
          loginTime: new Date().toISOString()
        }));

        showAlert('Login successful! Redirecting...', 'success');
        setTimeout(() => {
          navigate('/dashboard');
        }, 1000);
      } else {
        showAlert('Invalid credentials. Please try again.', 'danger');
      }
    } catch (error) {
      showAlert('An error occurred. Please try again.', 'danger');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-page">
      <img src="/img/cuab2.png" alt="CUAB Background" className="bg-float" />
      
      {isLoading && (
        <div className="spinner-overlay">
          <div className="spinner">
            <div className="spinner-circle"></div>
          </div>
        </div>
      )}
      
      <Header />

      <div className="container py-5 login-container">
        <div className="row justify-content-center">
          <div className="col-md-6 col-lg-5">
            <div className="card login-card border-0">
              <div className="card-body p-4">
                <h2 className="card-title text-center mb-4 text-success">Student Login</h2>
                
                <form onSubmit={handleSubmit} className={isValidated ? 'was-validated' : ''} noValidate>
                  <div className="mb-3">
                    <label htmlFor="email" className="form-label">Email/AppID/Matric No</label>
                    <input
                      type="email"
                      className={`form-control ${errors.email ? 'is-invalid' : ''}`}
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      required
                    />
                    <div className="invalid-feedback">
                      {errors.email || 'Please enter your registered email.'}
                    </div>
                  </div>

                  <div className="mb-3">
                    <label htmlFor="password" className="form-label">Password</label>
                    <input
                      type="password"
                      className={`form-control ${errors.password ? 'is-invalid' : ''}`}
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Enter password"
                      required
                    />
                    <div className="invalid-feedback">
                      {errors.password || 'Please enter your password.'}
                    </div>
                  </div>

                  <div className="d-flex justify-content-between mb-3">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        id="rememberMe"
                        name="rememberMe"
                        checked={formData.rememberMe}
                        onChange={handleInputChange}
                      />
                      <label className="form-check-label" htmlFor="rememberMe">
                        Remember me
                      </label>
                    </div>
                    <Link to="/forgot-password" className="forgot-password text-decoration-none">
                      Forgot password?
                    </Link>
                  </div>

                  <button type="submit" className="btn btn-cuab btn-lg w-100 text-white" disabled={isLoading}>
                    {isLoading ? 'Logging in...' : 'Login'}
                  </button>

                  {alert.show && (
                    <div className={`alert alert-${alert.type} mt-3`} role="alert">
                      {alert.message}
                    </div>
                  )}
                </form>

                <div className="text-center mt-4">
                  <p>Don't have an account? <Link to="/apply" className="text-success text-decoration-none">Apply Now</Link></p>
                </div>

                <div className="demo-credentials">
                  <small className="text-muted">
                    <strong>Demo Credentials:</strong><br />
                    Email: <EMAIL><br />
                    Password: Demo@1234
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Login;
