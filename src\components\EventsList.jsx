import React from 'react';
import './EventsList.css';

const EventsList = ({ events }) => {
  return (
    <div className="events-list">
      {events.map((event, index) => (
        <div key={index} className="event-item">
          <div className="event-date">
            <div className="day">{event.day}</div>
            <div className="month">{event.month}</div>
          </div>
          <div className="event-details">
            <h4>{event.title}</h4>
            <p>{event.description}</p>
            <div className="event-time">
              <i className="far fa-clock"></i> {event.time}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default EventsList;
