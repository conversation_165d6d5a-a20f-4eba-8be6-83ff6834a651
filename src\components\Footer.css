/* Footer Styles */
.footer {
  background: var(--gradient-primary);
  color: var(--text-light);
  padding: 3rem 0 1rem;
  margin-top: auto;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-yellow);
}

.footer h5 {
  color: var(--text-light);
  font-weight: 600;
  margin-bottom: 1.2rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.footer h5::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 2px;
  background: var(--secondary-color);
}

.footer p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.footer .list-unstyled li {
  margin-bottom: 0.5rem;
}

.footer .list-unstyled a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: var(--transition);
}

.footer .list-unstyled a:hover {
  color: var(--secondary-color);
  text-decoration: none;
}

.social-links a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  transition: var(--transition);
  margin-right: 0.5rem;
  text-decoration: none;
}

.social-links a:hover {
  background: var(--gradient-yellow);
  color: var(--primary-color);
  transform: translateY(-3px);
  text-decoration: none;
}

.footer hr {
  border-color: rgba(255, 255, 255, 0.2);
}

.footer .text-center p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer .col-md-4 {
    text-align: center;
    margin-bottom: 2rem;
  }
  
  .footer h5::after {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .social-links {
    justify-content: center;
  }
}
