import React from 'react';
import Calendar from './Calendar';
import EventsList from './EventsList';
import './MainContent.css';

const MainContent = ({ sidebarCollapsed, onToggleSidebar }) => {
  const events = [
    {
      day: "15",
      month: "Jun",
      title: "Midterm Examinations",
      description: "All departments - Main Campus",
      time: "8:00 AM - 11:00 AM"
    },
    {
      day: "20",
      month: "Jun",
      title: "Career Fair",
      description: "University Conference Center",
      time: "10:00 AM - 4:00 PM"
    },
    {
      day: "25",
      month: "Jun",
      title: "Guest Lecture: AI in Education",
      description: "Dr. <PERSON>",
      time: "2:00 PM - 4:00 PM"
    }
  ];

  return (
    <div className={`main-content ${sidebarCollapsed ? 'expanded' : ''}`}>
      <div className="header">
        <button className="toggle-sidebar" onClick={onToggleSidebar}>
          <i className="fas fa-bars"></i>
        </button>
        <h1>Dashboard</h1>
      </div>

      <div className="welcome-message">
        <p>Welcome to your student portal. Check the calendar for important dates and view upcoming events below.</p>
      </div>

      <div className="dashboard-grid">
        {/* Calendar Card */}
        <div className="card">
          <div className="card-header">
            <h3>School Calendar</h3>
            <a href="#full-calendar" className="view-all">View Full Calendar</a>
          </div>
          <Calendar />
        </div>

        {/* Events Card */}
        <div className="card">
          <div className="card-header">
            <h3>Upcoming Events</h3>
            <a href="#all-events" className="view-all">View All Events</a>
          </div>
          <EventsList events={events} />
        </div>
      </div>
    </div>
  );
};

export default MainContent;
