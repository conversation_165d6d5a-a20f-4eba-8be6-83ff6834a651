/* Course Registration Page Styles */
.course-registration-page {
  min-height: 100vh;
  background-color: var(--bg-light);
  display: flex;
  font-family: 'Segoe UI', '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background: linear-gradient(145deg, var(--primary-color), var(--primary-dark));
  color: white;
  height: 100vh;
  position: fixed;
  padding: 20px 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.sidebar-header {
  text-align: center;
  padding: 0 20px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
}

.school-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 15px;
  border-radius: 50%;
  border: 3px solid var(--secondary-color);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
}

.school-logo img {
  width: 90%;
  height: 90%;
  object-fit: contain;
}

.sidebar-header h2 {
  color: var(--secondary-color);
  margin-bottom: 5px;
  font-size: 18px;
}

.sidebar-menu {
  padding: 0 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 8px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
}

.menu-item.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
}

.menu-item i {
  margin-right: 15px;
  width: 20px;
  text-align: center;
  font-size: 18px;
}

.menu-text {
  white-space: nowrap;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 35px;
  transition: margin-left 0.3s ease;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow);
  padding: 30px;
  margin-bottom: 30px;
  text-align: center;
}

.page-header h2 {
  color: var(--primary-color);
  font-weight: 700;
  margin-bottom: 10px;
}

.student-info-card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow);
  padding: 30px;
  margin-bottom: 30px;
}

.student-info-card h4 {
  color: var(--primary-color);
  font-weight: 700;
  margin-bottom: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.info-section div {
  margin-bottom: 10px;
  line-height: 1.6;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

/* Course Selection */
.course-selection-section {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow);
  padding: 30px;
  margin-bottom: 30px;
}

.course-selection-section h4 {
  color: var(--primary-color);
  font-weight: 700;
  margin-bottom: 25px;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.course-card {
  border: 1px solid rgba(46, 125, 50, 0.1);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
  border-color: var(--primary-light);
  background: rgba(76, 175, 80, 0.05);
}

.form-check {
  padding: 20px;
  margin: 0;
  height: 100%;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.form-check-input {
  margin-top: 4px;
  flex-shrink: 0;
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.form-check-label {
  cursor: pointer;
  line-height: 1.5;
}

.badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  margin-right: 8px;
  margin-top: 8px;
}

.badge.credits {
  background: #e3f2fd;
  color: #1565c0;
}

.badge.compulsory {
  background: #e8f5e8;
  color: #2e7d32;
}

.badge.elective {
  background: #f3e5f5;
  color: #7b1fa2;
}

.selection-summary {
  background: var(--bg-light);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
}

.selection-summary p {
  margin-bottom: 15px;
  font-weight: 500;
  color: var(--primary-color);
}

/* Registered Courses */
.registered-courses-section {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow);
  padding: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-header h4 {
  color: var(--primary-color);
  font-weight: 700;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.table-responsive {
  overflow-x: auto;
}

.courses-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.courses-table th,
.courses-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid var(--medium-gray);
}

.courses-table th {
  background: var(--bg-light);
  color: var(--primary-color);
  font-weight: 600;
  border-bottom: 2px solid var(--primary-color);
}

.courses-table tbody tr:hover {
  background: rgba(46, 125, 50, 0.05);
}

.courses-table tfoot th {
  background: var(--primary-color);
  color: white;
  font-weight: 700;
}

.text-center {
  text-align: center;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.btn-primary {
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(90deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.2);
}

.btn-print {
  background: var(--secondary-color);
  color: var(--text-dark);
}

.btn-print:hover {
  background: var(--secondary-light);
}

.btn-download {
  background: var(--primary-light);
  color: white;
}

.btn-download:hover {
  background: var(--primary-color);
}

.btn-remove {
  background: #dc3545;
  color: white;
  padding: 6px 12px;
  font-size: 12px;
}

.btn-remove:hover {
  background: #c82333;
}

/* Mobile Toggle */
.sidebar-toggle {
  display: none;
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1100;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 15px;
  cursor: pointer;
  font-size: 18px;
  box-shadow: var(--shadow);
}

/* Responsive Design */
@media (max-width: 991px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .sidebar-toggle {
    display: block;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .courses-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .action-buttons {
    width: 100%;
    justify-content: center;
  }

  .courses-table {
    font-size: 14px;
  }

  .courses-table th,
  .courses-table td {
    padding: 10px 8px;
  }
}

@media (max-width: 576px) {
  .page-header,
  .student-info-card,
  .course-selection-section,
  .registered-courses-section {
    padding: 20px;
    border-radius: 12px;
  }

  .btn {
    padding: 8px 16px;
    font-size: 13px;
  }
}

/* Print Styles */
@media print {
  .sidebar,
  .sidebar-toggle,
  .action-buttons,
  .course-selection-section {
    display: none !important;
  }

  .main-content {
    margin-left: 0 !important;
    padding: 0 !important;
  }

  .page-header {
    text-align: center;
    margin-bottom: 20px;
  }

  .courses-table {
    border-collapse: collapse;
    width: 100%;
  }

  .courses-table th,
  .courses-table td {
    border: 1px solid #000;
    padding: 8px;
  }
}
