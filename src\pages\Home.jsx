import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Header';
import Footer from '../components/Footer';
import SecurityAlert from '../components/SecurityAlert';
import './Home.css';

const Home = () => {
  const [showSecurityAlert, setShowSecurityAlert] = useState(false);
  const [academicYear, setAcademicYear] = useState('');

  useEffect(() => {
    // Show security alert once per session
    if (!sessionStorage.getItem('securityAlertShown')) {
      setShowSecurityAlert(true);
      sessionStorage.setItem('securityAlertShown', 'true');
    }

    // Set academic year
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;
    setAcademicYear(`${currentYear}/${nextYear}`);

    // Add scroll effect to navbar
    const handleScroll = () => {
      const navbar = document.querySelector('.navbar');
      if (navbar) {
        if (window.scrollY > 50) {
          navbar.classList.add('scrolled');
        } else {
          navbar.classList.remove('scrolled');
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleCloseAlert = () => {
    setShowSecurityAlert(false);
  };

  return (
    <div className="home-page">
      <Header />
      
      {showSecurityAlert && (
        <SecurityAlert onClose={handleCloseAlert} />
      )}

      {/* Hero Section */}
      <section className="hero text-center">
        <div className="container">
          <h1 className="display-4">Welcome to Crescent University PG Admission Portal</h1>
          <p className="lead">
            <strong>
              Admissions are now open for {academicYear} session.<br />
              Shape your future with our world-class postgraduate programmes.
            </strong>
          </p>
          <Link to="/login" className="btn btn-success btn-lg">Apply for PG Admission</Link>
        </div>
      </section>

      {/* About Portal Section */}
      <section className="container py-5">
        <div className="row align-items-center">
          <div className="col-md-7">
            <h2>How to Apply for Postgraduate Admission</h2>
            <p>
              The Crescent University Abeokuta Postgraduate Portal is designed to facilitate the application process for prospective postgraduate students. The portal provides a user-friendly interface for applicants to create profiles, submit applications, and track their admission status.
            </p>
            <p>
              Applicants should read through the requirements for admission in the programme of choice. Thereafter, the applicant is expected to pay the required application fee of Twenty Thousand Naira only (20,000.00) using Master Card and then complete the application form online.
            </p>
            <p>Required documents include:</p>
            <ul>
              <li>Scanned copies of certificates (WASC/GCE O Level, Bachelor's Degree/HND)</li>
              <li>NYSC Discharge Certificate</li>
              <li>Passport photographs</li>
            </ul>
            <Link to="/login" className="btn btn-success mt-3">Read More About Application Process</Link>
          </div>
          <div className="col-md-5">
            <div className="position-relative">
              <img 
                src="/img/Cuabaud.jpeg" 
                alt="CUAB Campus" 
                className="img-fluid rounded shadow-lg campus-image"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="cta-section text-center">
        <div className="container">
          <h3 className="mb-4">Ready to begin your postgraduate journey?</h3>
          <Link to="/login" className="btn btn-light btn-lg">Start Application</Link>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Home;
