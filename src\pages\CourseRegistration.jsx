import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import './CourseRegistration.css';

const CourseRegistration = () => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const [selectedCourses, setSelectedCourses] = useState([]);
  const [registeredCourses, setRegisteredCourses] = useState([]);
  const [profileData, setProfileData] = useState({
    name: '<PERSON><PERSON>',
    appId: 'APP123456',
    programme: 'Masters',
    course: 'Computer Science',
    email: '<EMAIL>',
    phone: '+2348165473929'
  });

  const location = useLocation();

  const menuItems = [
    { href: "/dashboard", icon: "fas fa-home", text: "Dashboard" },
    { href: "/profile", icon: "fas fa-user", text: "Profile" },
    { href: "/payment", icon: "fas fa-credit-card", text: "Payment" },
    { href: "/course-registration", icon: "fas fa-book", text: "Course Registration" },
    { href: "/login", icon: "fas fa-sign-out-alt", text: "Logout" }
  ];

  const coursesByProgramme = {
    'PGD': [
      { code: 'PGD 101', title: 'Research Methods', credits: 2, status: 'C' },
      { code: 'PGD 102', title: 'Advanced Statistics', credits: 3, status: 'C' },
      { code: 'PGD 103', title: 'Seminar in Discipline', credits: 2, status: 'C' },
      { code: 'PGD 104', title: 'Project', credits: 4, status: 'C' },
      { code: 'PGD 105', title: 'Data Analysis', credits: 3, status: 'E' },
      { code: 'PGD 106', title: 'Academic Writing', credits: 2, status: 'E' }
    ],
    'Masters': [
      { code: 'MSC 201', title: 'Advanced Research Methods', credits: 3, status: 'C' },
      { code: 'MSC 202', title: 'Thesis Writing', credits: 4, status: 'C' },
      { code: 'MSC 203', title: 'Seminar in Major Area', credits: 2, status: 'C' },
      { code: 'MSC 204', title: 'Elective Course', credits: 2, status: 'E' },
      { code: 'MSC 205', title: 'Advanced Data Analysis', credits: 3, status: 'E' },
      { code: 'MSC 206', title: 'Research Ethics', credits: 2, status: 'E' },
      { code: 'MSC 207', title: 'Academic Publishing', credits: 2, status: 'E' }
    ],
    'MPhil/PhD': [
      { code: 'MPHIL 301', title: 'Research Proposal', credits: 3, status: 'C' },
      { code: 'MPHIL 302', title: 'Advanced Seminar', credits: 2, status: 'C' },
      { code: 'MPHIL 303', title: 'Dissertation', credits: 6, status: 'C' },
      { code: 'MPHIL 304', title: 'Elective', credits: 2, status: 'E' },
      { code: 'MPHIL 305', title: 'Advanced Research Design', credits: 3, status: 'E' },
      { code: 'MPHIL 306', title: 'Academic Leadership', credits: 2, status: 'E' },
      { code: 'MPHIL 307', title: 'Research Funding', credits: 2, status: 'E' }
    ],
    'PhD': [
      { code: 'PHD 401', title: 'Doctoral Seminar', credits: 2, status: 'C' },
      { code: 'PHD 402', title: 'Thesis', credits: 6, status: 'C' },
      { code: 'PHD 403', title: 'Advanced Elective', credits: 3, status: 'E' },
      { code: 'PHD 404', title: 'Research Publication', credits: 2, status: 'E' },
      { code: 'PHD 405', title: 'Advanced Research Methods', credits: 3, status: 'E' },
      { code: 'PHD 406', title: 'Academic Writing', credits: 2, status: 'E' },
      { code: 'PHD 407', title: 'Research Ethics', credits: 2, status: 'E' },
      { code: 'PHD 408', title: 'Academic Leadership', credits: 2, status: 'E' }
    ]
  };

  useEffect(() => {
    // Load profile data from localStorage
    const storedProfile = localStorage.getItem('pg_profile');
    if (storedProfile) {
      const parsedProfile = JSON.parse(storedProfile);
      setProfileData({
        name: `${parsedProfile.surname || ''} ${parsedProfile.firstname || ''} ${parsedProfile.middlename || ''}`.trim(),
        appId: parsedProfile.appId || 'APP123456',
        programme: parsedProfile.programme || 'Masters',
        course: parsedProfile.course || 'Computer Science',
        email: parsedProfile.email || '<EMAIL>',
        phone: parsedProfile.phone || '+2348165473929'
      });
    }

    // Load registered courses from localStorage
    const storedCourses = localStorage.getItem('pg_registered_courses');
    if (storedCourses) {
      setRegisteredCourses(JSON.parse(storedCourses));
    }
  }, []);

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  const isActive = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  const handleCourseSelection = (course, isChecked) => {
    if (isChecked) {
      setSelectedCourses(prev => [...prev, course]);
    } else {
      setSelectedCourses(prev => prev.filter(c => c.code !== course.code));
    }
  };

  const handleRegisterCourses = (e) => {
    e.preventDefault();
    
    if (selectedCourses.length === 0) {
      alert('Please select at least one course to register.');
      return;
    }

    const newRegisteredCourses = [...registeredCourses, ...selectedCourses];
    setRegisteredCourses(newRegisteredCourses);
    localStorage.setItem('pg_registered_courses', JSON.stringify(newRegisteredCourses));
    setSelectedCourses([]);
    
    // Uncheck all checkboxes
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
    
    alert('Courses registered successfully!');
  };

  const handleRemoveCourse = (courseCode) => {
    const updatedCourses = registeredCourses.filter(course => course.code !== courseCode);
    setRegisteredCourses(updatedCourses);
    localStorage.setItem('pg_registered_courses', JSON.stringify(updatedCourses));
  };

  const getTotalCredits = () => {
    return registeredCourses.reduce((total, course) => total + course.credits, 0);
  };

  const getSelectedCredits = () => {
    return selectedCourses.reduce((total, course) => total + course.credits, 0);
  };

  const availableCourses = coursesByProgramme[profileData.programme] || [];
  const unregisteredCourses = availableCourses.filter(
    course => !registeredCourses.some(regCourse => regCourse.code === course.code)
  );

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = () => {
    // This would typically use a library like html2pdf or jsPDF
    alert('PDF download functionality would be implemented here.');
  };

  return (
    <div className="course-registration-page">
      {/* Sidebar Toggle Button (Mobile) */}
      <button className="sidebar-toggle" onClick={toggleSidebar}>
        <i className="fas fa-bars"></i>
      </button>

      {/* Sidebar */}
      <div className={`sidebar ${sidebarActive ? 'active' : ''}`}>
        <div className="sidebar-header">
          <div className="school-logo">
            <img src="/img/unknown.png" alt="University Logo" />
          </div>
          <h2>UNIVERSITY PORTAL</h2>
          <p>Course Registration</p>
        </div>
        <div className="sidebar-menu">
          {menuItems.map((item, index) => (
            <Link 
              key={index} 
              to={item.href} 
              className={`menu-item ${isActive(item.href)}`}
              onClick={() => window.innerWidth <= 768 && setSidebarActive(false)}
            >
              <i className={item.icon}></i>
              <span className="menu-text">{item.text}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="main-content">
        <div className="container">
          <div className="page-header">
            <h2>Course Registration Portal</h2>
            <p>Register for courses for the 2023/2024 Academic Session</p>
          </div>

          {/* Student Information */}
          <div className="student-info-card">
            <h4>Student Information</h4>
            <div className="info-grid">
              <div className="info-section">
                <div><strong>Name:</strong> {profileData.name}</div>
                <div><strong>Application ID:</strong> {profileData.appId}</div>
                <div><strong>Programme:</strong> {profileData.programme}</div>
                <div><strong>Course of Study:</strong> {profileData.course}</div>
              </div>
              <div className="info-section">
                <div><strong>Email:</strong> {profileData.email}</div>
                <div><strong>Phone:</strong> {profileData.phone}</div>
                <div><strong>Session:</strong> 2023/2024</div>
                <div><strong>Status:</strong> <span className="status-badge active">Active</span></div>
              </div>
            </div>
          </div>

          {/* Course Selection */}
          {unregisteredCourses.length > 0 && (
            <div className="course-selection-section">
              <h4>Available Courses</h4>
              <form onSubmit={handleRegisterCourses}>
                <div className="courses-grid">
                  {unregisteredCourses.map((course, index) => (
                    <div key={course.code} className="course-card">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`course${index}`}
                          onChange={(e) => handleCourseSelection(course, e.target.checked)}
                        />
                        <label className="form-check-label" htmlFor={`course${index}`}>
                          <strong>{course.code}</strong>: {course.title}<br />
                          <span className={`badge credits`}>{course.credits} Credits</span>
                          <span className={`badge ${course.status === 'C' ? 'compulsory' : 'elective'}`}>
                            {course.status === 'C' ? 'Compulsory' : 'Elective'}
                          </span>
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
                
                {selectedCourses.length > 0 && (
                  <div className="selection-summary">
                    <p>Selected: {selectedCourses.length} courses ({getSelectedCredits()} credits)</p>
                    <button type="submit" className="btn btn-primary">
                      Register Selected Courses
                    </button>
                  </div>
                )}
              </form>
            </div>
          )}

          {/* Registered Courses */}
          <div className="registered-courses-section">
            <div className="section-header">
              <h4>Registered Courses</h4>
              <div className="action-buttons">
                <button className="btn btn-print" onClick={handlePrint}>
                  <i className="fas fa-print"></i> Print
                </button>
                <button className="btn btn-download" onClick={handleDownloadPDF}>
                  <i className="fas fa-download"></i> Download PDF
                </button>
              </div>
            </div>
            
            <div className="table-responsive">
              <table className="courses-table">
                <thead>
                  <tr>
                    <th>Course Code</th>
                    <th>Title</th>
                    <th>Credit Units</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {registeredCourses.length > 0 ? (
                    registeredCourses.map((course) => (
                      <tr key={course.code}>
                        <td>{course.code}</td>
                        <td>{course.title}</td>
                        <td>{course.credits}</td>
                        <td>
                          <span className={`badge ${course.status === 'C' ? 'compulsory' : 'elective'}`}>
                            {course.status === 'C' ? 'Compulsory' : 'Elective'}
                          </span>
                        </td>
                        <td>
                          <button 
                            className="btn btn-remove"
                            onClick={() => handleRemoveCourse(course.code)}
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="5" className="text-center">No courses registered yet</td>
                    </tr>
                  )}
                </tbody>
                <tfoot>
                  <tr>
                    <th colSpan="2">Total Credits</th>
                    <th>{getTotalCredits()}</th>
                    <th colSpan="2"></th>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseRegistration;
