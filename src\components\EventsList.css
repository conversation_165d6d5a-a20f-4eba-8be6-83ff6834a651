.events-list {
    display: flex;
    flex-direction: column;
}

.event-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.event-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.event-date {
    background-color: var(--primary-light);
    color: white;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    min-width: 60px;
    margin-right: 15px;
    flex-shrink: 0;
}

.event-date .day {
    font-size: 20px;
    font-weight: bold;
    line-height: 1;
}

.event-date .month {
    font-size: 12px;
    text-transform: uppercase;
}

.event-details {
    flex: 1;
}

.event-details h4 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 16px;
}

.event-details p {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

.event-time {
    display: flex;
    align-items: center;
    color: var(--secondary-dark);
    font-size: 13px;
    margin-top: 5px;
}

.event-time i {
    margin-right: 5px;
}
