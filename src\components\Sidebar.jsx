import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Sidebar.css';

const Sidebar = ({ collapsed, onToggle }) => {
  const location = useLocation();
  
  const menuItems = [
    { href: "/dashboard", icon: "fas fa-home", text: "Dashboard" },
    { href: "/profile", icon: "fas fa-user", text: "Profile" },
    { href: "/payment", icon: "fas fa-credit-card", text: "Payment" },
    { href: "/course-registration", icon: "fas fa-user-graduate", text: "Course Registration" },
    { href: "/login", icon: "fas fa-sign-out-alt", text: "Log Out" }
  ];

  return (
    <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header">
        <div className="school-logo">
          <img src="/img/unknown.png" alt="University Logo" />
        </div>
        <h2>UNIVERSITY PORTAL</h2>
        <p>Dashboard</p>
      </div>
      <div className="sidebar-menu">
        {menuItems.map((item, index) => (
          <Link 
            key={index} 
            to={item.href} 
            className={`menu-link ${location.pathname === item.href ? 'active' : ''}`}
          >
            <div className="menu-item">
              <i className={item.icon}></i>
              <span className="menu-text">{item.text}</span>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default Sidebar;
