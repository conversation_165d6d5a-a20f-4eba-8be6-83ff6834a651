/* Login Page Styles */
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(205, 220, 57, 0.8), rgba(165, 214, 167, 0.8));
  color: #222;
  position: relative;
  overflow-x: hidden;
}

/* Floating background image */
.bg-float {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 40%;
  min-width: 400px;
  max-width: 600px;
  opacity: 0.5;
  z-index: 0;
  animation: float 15s ease-in-out infinite alternate;
  filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.2));
  pointer-events: none;
  user-select: none;
}

@keyframes float {
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(-20px, -15px) rotate(1deg); }
  50% { transform: translate(-40px, -30px) rotate(-1deg); }
  75% { transform: translate(-20px, -45px) rotate(1deg); }
  100% { transform: translate(0, -60px) rotate(0deg); }
}

/* Spinner Animation */
.spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 60px;
  height: 60px;
}

.spinner-circle {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border: 8px solid #f3f3f3;
  border-top-color: #388e3c;
  border-radius: 50%;
  animation: spinner 1s linear infinite;
}

@keyframes spinner {
  to { transform: rotate(360deg); }
}

.login-container {
  backdrop-filter: blur(2px);
  min-height: 80vh;
  display: flex;
  align-items: center;
}

.login-card {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 8px 32px rgba(30, 60, 114, 0.1);
  z-index: 2;
  transition: transform 0.3s ease;
  overflow: hidden;
}

.login-card:hover {
  transform: translateY(-5px);
}

.btn-cuab {
  font-size: 1.1rem;
  border-radius: 8px;
  background-color: #2e7d32;
  border-color: #2e7d32;
  transition: all 0.3s ease;
}

.btn-cuab:hover {
  background-color: #1b5e20;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
}

.btn-cuab:disabled {
  background-color: #6c757d;
  border-color: #6c757d;
  transform: none;
  box-shadow: none;
}

.form-control {
  transition: all 0.3s ease;
  border: 1px solid #ced4da;
}

.form-control:focus {
  border-color: #aeea00;
  box-shadow: 0 0 0 0.25rem rgba(174, 234, 0, 0.3);
  transform: translateY(-2px);
}

.form-label {
  color: #2e7d32;
  font-weight: 500;
  transition: color 0.3s ease;
}

.text-center a {
  color: #2e7d32;
  transition: all 0.3s ease;
}

.text-center a:hover {
  color: #388e3c;
  text-decoration: none;
}

.forgot-password {
  font-size: 0.9rem;
  color: #2e7d32;
}

.forgot-password:hover {
  color: #388e3c;
}

.demo-credentials {
  background-color: rgba(248, 249, 250, 0.7);
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.alert {
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

/* Responsive Design */
@media (max-width: 991px) {
  .bg-float {
    width: 60%;
    min-width: 300px;
    opacity: 0.4;
  }
}

@media (max-width: 768px) {
  .bg-float {
    width: 70%;
    opacity: 0.3;
  }
  
  .login-container {
    padding: 2rem 0;
  }
}

@media (max-width: 576px) {
  .bg-float {
    width: 90%;
    min-width: 250px;
    opacity: 0.2;
  }
  
  .login-card {
    margin: 1rem;
  }
  
  .card-body {
    padding: 2rem 1.5rem !important;
  }
}
