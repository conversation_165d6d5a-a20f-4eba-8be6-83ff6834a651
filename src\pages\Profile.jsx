import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Profile.css';

const Profile = () => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const [profileData, setProfileData] = useState({
    fullName: 'Toriola Mu<PERSON>zeen',
    studentId: '2023001',
    department: 'Computer Science',
    level: '300 Level',
    college: 'CICOT',
    matricNumber: 'S121202030',
    admissionYear: '2021',
    dateOfBirth: 'January 1, 2000',
    email: '<EMAIL>',
    phone: '+234 ************',
    address: 'Ilaro, Ogun State, Nigeria',
    gender: 'Male'
  });

  const location = useLocation();

  const menuItems = [
    { href: "/dashboard", icon: "fas fa-home", text: "Dashboard" },
    { href: "/profile", icon: "fas fa-user", text: "Profile" },
    { href: "/payment", icon: "fas fa-credit-card", text: "Payment" },
    { href: "/course-registration", icon: "fas fa-book", text: "Course Registration" },
    { href: "/login", icon: "fas fa-sign-out-alt", text: "Logout" }
  ];

  useEffect(() => {
    // Load profile data from localStorage if available
    const storedProfile = localStorage.getItem('pg_profile');
    if (storedProfile) {
      const parsedProfile = JSON.parse(storedProfile);
      setProfileData(prev => ({
        ...prev,
        fullName: `${parsedProfile.surname || ''} ${parsedProfile.firstname || ''} ${parsedProfile.middlename || ''}`.trim(),
        email: parsedProfile.email || prev.email,
        phone: parsedProfile.phone || prev.phone,
        department: parsedProfile.course || prev.department,
        studentId: parsedProfile.appId || prev.studentId
      }));
    }

    // Add loaded class for animations
    document.body.classList.add('loaded');

    // Cleanup function
    return () => {
      document.body.classList.remove('loaded');
    };
  }, []);

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  const handleClickOutside = (e) => {
    if (window.innerWidth <= 992) {
      const sidebar = document.getElementById('sidebar');
      const toggleBtn = document.querySelector('.mobile-toggle');
      if (sidebar && !sidebar.contains(e.target) && e.target !== toggleBtn) {
        setSidebarActive(false);
      }
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const isActive = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  return (
    <div className="profile-page">
      {/* Mobile Toggle Button */}
      <button className="mobile-toggle" onClick={toggleSidebar}>
        <i className="fas fa-bars"></i>
      </button>

      {/* Sidebar Navigation */}
      <div className={`sidebar ${sidebarActive ? 'active' : ''}`} id="sidebar">
        <div className="sidebar-header">
          <img src="/img/unknown.png" alt="Student Profile" />
          <h5>{profileData.fullName}</h5>
          <small>Student ID: {profileData.studentId}</small>
        </div>
        <div className="sidebar-menu">
          {menuItems.map((item, index) => (
            <Link 
              key={index} 
              to={item.href} 
              className={`menu-item ${isActive(item.href)}`}
              onClick={() => window.innerWidth <= 992 && setSidebarActive(false)}
            >
              <i className={item.icon}></i>
              <span>{item.text}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="main-content">
        {/* Profile Header */}
        <div className="profile-header">
          <div className="row align-items-center">
            <div className="col-lg-3 text-center text-lg-start mb-4 mb-lg-0">
              <img src="/img/unknown.png" alt="Profile Picture" className="profile-image" />
            </div>
            <div className="col-lg-9 text-center text-lg-start">
              <h2 className="mb-3">{profileData.fullName}</h2>
              <p className="text-muted mb-2">
                <i className="fas fa-id-card me-2 text-accent"></i> 
                Student ID: {profileData.studentId}
              </p>
              <p className="text-muted mb-2">
                <i className="fas fa-graduation-cap me-2 text-accent"></i> 
                Department of {profileData.department}
              </p>
              <p className="text-muted">
                <i className="fas fa-university me-2 text-accent"></i> 
                {profileData.level}
              </p>
            </div>
          </div>
        </div>

        {/* Profile Information Sections */}
        <div className="row">
          {/* Personal Information */}
          <div className="col-lg-6">
            <div className="profile-card">
              <h4 className="section-title">Personal Information</h4>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Full Name</div>
                <div className="col-md-8 info-value">{profileData.fullName}</div>
              </div>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Date of Birth</div>
                <div className="col-md-8 info-value">{profileData.dateOfBirth}</div>
              </div>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Email</div>
                <div className="col-md-8 info-value">{profileData.email}</div>
              </div>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Phone</div>
                <div className="col-md-8 info-value">{profileData.phone}</div>
              </div>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Address</div>
                <div className="col-md-8 info-value">{profileData.address}</div>
              </div>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Gender</div>
                <div className="col-md-8 info-value">{profileData.gender}</div>
              </div>
            </div>
          </div>

          {/* Academic Information */}
          <div className="col-lg-6">
            <div className="profile-card">
              <h4 className="section-title">Academic Information</h4>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Level</div>
                <div className="col-md-8 info-value">{profileData.level}</div>
              </div>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Department</div>
                <div className="col-md-8 info-value">{profileData.department}</div>
              </div>
              <div className="row mb-3">
                <div className="col-md-4 info-label">College</div>
                <div className="col-md-8 info-value">{profileData.college}</div>
              </div>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Matric Number</div>
                <div className="col-md-8 info-value">{profileData.matricNumber}</div>
              </div>
              <div className="row mb-3">
                <div className="col-md-4 info-label">Admission Year</div>
                <div className="col-md-8 info-value">{profileData.admissionYear}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
