.calendar {
    width: 100%;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.calendar-nav {
    display: flex;
    gap: 10px;
}

.calendar-nav button {
    background-color: var(--primary-light);
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    color: white;
    transition: background-color 0.3s ease;
}

.calendar-nav button:hover {
    background-color: var(--primary-color);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-day-header {
    text-align: center;
    font-weight: bold;
    padding: 5px;
    color: var(--primary-color);
}

.calendar-day {
    text-align: center;
    padding: 10px 5px;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-day:hover {
    background-color: var(--primary-light);
    color: white;
}

.calendar-day.today {
    background-color: var(--secondary-color);
    color: white;
    font-weight: bold;
}

.calendar-day.other-month {
    color: #aaa;
}

.calendar-day.event {
    position: relative;
}

.calendar-day.event::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 5px;
    height: 5px;
    background-color: var(--primary-color);
    border-radius: 50%;
}
