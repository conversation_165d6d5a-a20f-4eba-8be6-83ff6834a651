/* Main Content Styles */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 20px;
    transition: margin-left 0.3s ease;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.header h1 {
    color: var(--primary-color);
}

.toggle-sidebar {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 20px;
    cursor: pointer;
    display: none;
}

.welcome-message {
    background-color: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    text-align: center;
}

.welcome-message p {
    color: #555;
    line-height: 1.5;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.card-header h3 {
    color: var(--primary-color);
}

.card-header .view-all {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 14px;
}

.main-content.expanded {
    margin-left: var(--sidebar-collapsed-width);
}

/* Responsive Styles for Main Content */
@media (max-width: 992px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-left: var(--sidebar-collapsed-width);
    }
    
    .toggle-sidebar {
        display: block;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 15px;
    }
    
    .card {
        padding: 15px;
    }
}
