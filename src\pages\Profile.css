/* Profile Page Styles */
.profile-page {
  min-height: 100vh;
  background-color: var(--light-gray);
  font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-dark);
  line-height: 1.6;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  background: linear-gradient(145deg, var(--primary-dark), var(--primary-color));
  color: white;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  z-index: 1000;
  box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.sidebar-header {
  padding: 30px 20px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  background-color: rgba(0, 0, 0, 0.1);
}

.sidebar-header img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.sidebar-header h5 {
  margin: 10px 0 5px;
  font-weight: 600;
  color: white;
}

.sidebar-header small {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
}

.sidebar-menu {
  padding: 20px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 15px 25px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  position: relative;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  transform: translateX(5px);
}

.menu-item.active {
  background: rgba(255, 255, 255, 0.2);
  font-weight: 500;
  border-left: 4px solid var(--secondary-color);
  box-shadow: inset 4px 0 8px rgba(0, 0, 0, 0.1);
}

.menu-item i {
  margin-right: 15px;
  width: 24px;
  text-align: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.menu-item:hover i {
  color: var(--secondary-light);
  transform: scale(1.1);
}

/* Main Content Styles */
.main-content {
  margin-left: 280px;
  padding: 35px;
  transition: all 0.3s ease;
  background-color: var(--light-gray);
}

.profile-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
  border-top: 4px solid var(--primary-color);
  animation: fadeInUp 0.6s ease-out;
}

.profile-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
  height: 100%;
  border-top: 4px solid var(--primary-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-light), var(--secondary-light));
}

.profile-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.profile-image {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  object-fit: cover;
  border: 6px solid white;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.section-title {
  color: var(--primary-color);
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 25px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--light-gray);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 50px;
  height: 2px;
  background: var(--secondary-color);
}

.info-label {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 8px;
}

.info-value {
  color: var(--dark-gray);
  margin-bottom: 8px;
}

.text-accent {
  color: var(--secondary-dark);
}

/* Mobile Toggle Button */
.mobile-toggle {
  display: none;
  position: fixed;
  top: 25px;
  left: 25px;
  background: var(--primary-color);
  border: none;
  padding: 12px 15px;
  border-radius: 50%;
  color: white;
  z-index: 1100;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.mobile-toggle:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-card:nth-child(1) {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.profile-card:nth-child(2) {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .sidebar {
    width: 240px;
  }
  .main-content {
    margin-left: 240px;
  }
}

@media (max-width: 992px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.active {
    transform: translateX(0);
    box-shadow: 5px 0 25px rgba(0, 0, 0, 0.2);
  }

  .main-content {
    margin-left: 0;
    padding: 25px;
  }

  .mobile-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .profile-header {
    text-align: center;
    padding: 25px;
  }

  .profile-image {
    width: 140px;
    height: 140px;
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .profile-header {
    padding: 20px;
  }
  
  .profile-card {
    padding: 25px;
  }
  
  .section-title {
    font-size: 1.3rem;
  }
}

@media (max-width: 576px) {
  .main-content {
    padding: 20px;
  }
  
  .profile-header, 
  .profile-card {
    padding: 20px;
    border-radius: 10px;
  }
  
  .profile-image {
    width: 120px;
    height: 120px;
  }
  
  .info-label, 
  .info-value {
    font-size: 0.9rem;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}
