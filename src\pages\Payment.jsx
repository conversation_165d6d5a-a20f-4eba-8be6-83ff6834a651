import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Payment.css';

const Payment = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [sidebarActive, setSidebarActive] = useState(false);
  const [paymentData, setPaymentData] = useState({
    applicationFee: { status: 'pending', amount: '₦20,000.00', reference: '' },
    acceptanceFee: { status: 'locked', amount: '₦50,000.00', reference: '' },
    schoolFees: { status: 'locked', amount: '₦250,000.00', reference: '' }
  });
  const [profileData, setProfileData] = useState({
    name: '<PERSON><PERSON>',
    appId: 'APP123456',
    programme: 'Masters',
    course: 'Computer Science'
  });

  const location = useLocation();

  const menuItems = [
    { href: "/dashboard", icon: "fas fa-home", text: "Dashboard" },
    { href: "/profile", icon: "fas fa-user", text: "Profile" },
    { href: "/payment", icon: "fas fa-credit-card", text: "Payment" },
    { href: "/course-registration", icon: "fas fa-book", text: "Course Registration" },
    { href: "/login", icon: "fas fa-sign-out-alt", text: "Logout" }
  ];

  const bankDetails = {
    bankName: "Zenith Bank",
    accountName: "Crescent University Abeokuta,Ogun State",
    accountNumber: "**********"
  };

  useEffect(() => {
    // Load profile data from localStorage
    const storedProfile = localStorage.getItem('pg_profile');
    if (storedProfile) {
      const parsedProfile = JSON.parse(storedProfile);
      setProfileData({
        name: `${parsedProfile.surname || ''} ${parsedProfile.firstname || ''} ${parsedProfile.middlename || ''}`.trim(),
        appId: parsedProfile.appId || 'APP123456',
        programme: parsedProfile.programme || 'Masters',
        course: parsedProfile.course || 'Computer Science'
      });
    }

    // Generate payment references
    setPaymentData(prev => ({
      ...prev,
      applicationFee: { ...prev.applicationFee, reference: `APF-2023${Math.floor(1000 + Math.random() * 9000)}` },
      acceptanceFee: { ...prev.acceptanceFee, reference: `ACP-2025${Math.floor(1000 + Math.random() * 9000)}` },
      schoolFees: { ...prev.schoolFees, reference: `SCF-2023${Math.floor(1000 + Math.random() * 9000)}` }
    }));
  }, []);

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  const isActive = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('Copied to clipboard!');
    });
  };

  const handleFileUpload = (paymentType, event) => {
    const file = event.target.files[0];
    if (file) {
      // Simulate file upload
      setPaymentData(prev => ({
        ...prev,
        [paymentType]: { ...prev[paymentType], status: 'uploaded' }
      }));

      // Unlock next payment step
      if (paymentType === 'applicationFee') {
        setPaymentData(prev => ({
          ...prev,
          acceptanceFee: { ...prev.acceptanceFee, status: 'pending' }
        }));
      } else if (paymentType === 'acceptanceFee') {
        setPaymentData(prev => ({
          ...prev,
          schoolFees: { ...prev.schoolFees, status: 'pending' }
        }));
      }

      alert('Receipt uploaded successfully! Your payment is being verified.');
    }
  };

  const navigateToStep = (step) => {
    if (step === 1) {
      setCurrentStep(1);
    } else if (step === 2 && paymentData.applicationFee.status === 'uploaded') {
      setCurrentStep(2);
    } else if (step === 3 && paymentData.acceptanceFee.status === 'uploaded') {
      setCurrentStep(3);
    } else {
      alert('Please complete the previous payment step first');
    }
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      pending: 'status-badge status-pending',
      uploaded: 'status-badge status-uploaded',
      verified: 'status-badge status-verified',
      locked: 'status-badge status-locked'
    };

    const statusText = {
      pending: 'Pending',
      uploaded: 'Receipt Uploaded',
      verified: 'Verified',
      locked: 'Locked'
    };

    return (
      <span className={statusClasses[status]}>
        {statusText[status]}
      </span>
    );
  };

  const renderPaymentSection = (paymentType, title, amount, reference) => {
    const payment = paymentData[paymentType];
    const isLocked = payment.status === 'locked';

    return (
      <div className={`payment-card ${isLocked ? 'locked' : ''}`}>
        <div className="payment-header">
          <h3>{title}</h3>
          {getStatusBadge(payment.status)}
        </div>

        <div className="fee-amount">
          <span className="amount">{amount}</span>
        </div>

        {!isLocked && (
          <>
            <div className="bank-details">
              <h4>Bank Transfer Details</h4>
              <div className="bank-info">
                <div className="bank-item">
                  <div className="bank-label">Bank Name</div>
                  <div className="bank-value">{bankDetails.bankName}</div>
                </div>
                <div className="bank-item">
                  <div className="bank-label">Account Name</div>
                  <div className="bank-value">{bankDetails.accountName}</div>
                </div>
                <div className="bank-item">
                  <div className="bank-label">Account Number</div>
                  <div className="bank-value">
                    {bankDetails.accountNumber}
                    <button 
                      className="copy-btn" 
                      onClick={() => copyToClipboard(bankDetails.accountNumber)}
                    >
                      Copy
                    </button>
                  </div>
                </div>
                <div className="bank-item">
                  <div className="bank-label">Payment Reference</div>
                  <div className="bank-value">
                    {reference}
                    <button 
                      className="copy-btn" 
                      onClick={() => copyToClipboard(reference)}
                    >
                      Copy
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="payment-instructions">
              <h4>Payment Instructions</h4>
              <ol>
                <li>Transfer the exact amount ({amount}) to the bank account above</li>
                <li>Use the provided payment reference exactly as shown</li>
                <li>After successful transfer, upload your payment receipt below</li>
                <li>Allow 24-48 hours for payment verification</li>
              </ol>
            </div>

            {payment.status !== 'uploaded' && payment.status !== 'verified' && (
              <div className="receipt-upload">
                <h4>Upload Payment Receipt</h4>
                <div className="upload-section">
                  <input
                    type="file"
                    id={`${paymentType}Receipt`}
                    accept="image/*,.pdf"
                    onChange={(e) => handleFileUpload(paymentType, e)}
                    style={{ display: 'none' }}
                  />
                  <label htmlFor={`${paymentType}Receipt`} className="upload-btn">
                    <i className="fas fa-upload"></i> Choose File
                  </label>
                  <p className="upload-note">Accepted formats: JPG, PNG, PDF (Max: 5MB)</p>
                </div>
              </div>
            )}

            {(payment.status === 'uploaded' || payment.status === 'verified') && (
              <div className="upload-success">
                <i className="fas fa-check-circle"></i>
                <p>Receipt uploaded successfully! Your payment is being verified.</p>
              </div>
            )}
          </>
        )}

        {isLocked && (
          <div className="locked-message">
            <i className="fas fa-lock"></i>
            <p>This section will unlock after the previous payment is verified</p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="payment-page">
      {/* Sidebar Toggle Button (Mobile) */}
      <button className="sidebar-toggle" onClick={toggleSidebar}>
        <i className="fas fa-bars"></i>
      </button>

      {/* Sidebar */}
      <div className={`sidebar ${sidebarActive ? 'active' : ''}`}>
        <div className="sidebar-header">
          <div className="school-logo">
            <img src="/img/unknown.png" alt="University Logo" />
          </div>
          <h2>UNIVERSITY PORTAL</h2>
          <p>Payment</p>
        </div>
        <div className="sidebar-menu">
          {menuItems.map((item, index) => (
            <Link 
              key={index} 
              to={item.href} 
              className={`menu-item ${isActive(item.href)}`}
              onClick={() => window.innerWidth <= 768 && setSidebarActive(false)}
            >
              <i className={item.icon}></i>
              <span className="menu-text">{item.text}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="main-content">
        <div className="container">
          <header className="page-header">
            <h1>University Payment Portal</h1>
            <p>Complete your payment process in 3 simple steps</p>
          </header>

          {/* Student Info */}
          <div className="student-info">
            <h3>Student Information</h3>
            <div className="info-grid">
              <div><strong>Name:</strong> {profileData.name}</div>
              <div><strong>Application ID:</strong> {profileData.appId}</div>
              <div><strong>Programme:</strong> {profileData.programme}</div>
              <div><strong>Course:</strong> {profileData.course}</div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="progress-container">
            <div className="progress-bar" style={{ width: `${(currentStep / 3) * 100}%` }}></div>
            <div 
              className={`progress-step ${currentStep >= 1 ? 'active' : ''}`} 
              onClick={() => navigateToStep(1)}
            >
              <div className="step-number">1</div>
              <div className="step-title">Application Fee</div>
            </div>
            <div 
              className={`progress-step ${currentStep >= 2 ? 'active' : ''}`} 
              onClick={() => navigateToStep(2)}
            >
              <div className="step-number">2</div>
              <div className="step-title">Acceptance Fee</div>
            </div>
            <div 
              className={`progress-step ${currentStep >= 3 ? 'active' : ''}`} 
              onClick={() => navigateToStep(3)}
            >
              <div className="step-number">3</div>
              <div className="step-title">School Fees</div>
            </div>
          </div>

          {/* Payment Sections */}
          <div className="payment-sections">
            {currentStep === 1 && renderPaymentSection(
              'applicationFee', 
              'Application Fee', 
              paymentData.applicationFee.amount, 
              paymentData.applicationFee.reference
            )}
            {currentStep === 2 && renderPaymentSection(
              'acceptanceFee', 
              'Acceptance Fee', 
              paymentData.acceptanceFee.amount, 
              paymentData.acceptanceFee.reference
            )}
            {currentStep === 3 && renderPaymentSection(
              'schoolFees', 
              'School Fees', 
              paymentData.schoolFees.amount, 
              paymentData.schoolFees.reference
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Payment;
