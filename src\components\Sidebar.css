/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--primary-color);
    color: white;
    height: 100vh;
    position: fixed;
    padding: 20px 0;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    transition: width 0.3s ease;
    z-index: 1000;
}

.sidebar-header {
    text-align: center;
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.school-logo {
    width: 100px;
    height: 100px;
    margin: 0 auto 15px;
    border-radius: 50%;
    border: 3px solid var(--secondary-color);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.school-logo img {
    width: 90%;
    height: 90%;
    object-fit: contain;
}

.sidebar-header h2 {
    color: var(--secondary-color);
    margin-bottom: 5px;
    font-size: 18px;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.sidebar-menu {
    margin-top: 20px;
}

.menu-link {
    display: block;
    text-decoration: none;
    color: white;
    transition: all 0.3s;
}

.menu-item {
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    position: relative;
}

.menu-item:hover {
    background-color: var(--primary-dark);
}

.menu-link.active .menu-item {
    background-color: var(--primary-dark);
    border-left: 4px solid var(--secondary-color);
}

.menu-item i {
    margin-right: 10px;
    color: var(--secondary-light);
    min-width: 20px;
    text-align: center;
    font-size: 18px;
}

.menu-item .menu-text {
    white-space: nowrap;
    transition: opacity 0.3s ease;
}

/* Sidebar toggle animation */
.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed .school-logo {
    width: 40px;
    height: 40px;
    border-width: 2px;
}

.sidebar.collapsed .sidebar-header h2,
.sidebar.collapsed .menu-text {
    display: none;
}

.sidebar.collapsed .menu-item {
    justify-content: center;
    padding: 15px 10px;
}

.sidebar.collapsed .menu-item i {
    margin-right: 0;
    font-size: 20px;
}

/* Responsive Styles for Sidebar */
@media (max-width: 768px) {
    .sidebar {
        width: var(--sidebar-collapsed-width);
    }
    
    .school-logo {
        width: 40px;
        height: 40px;
        border-width: 2px;
    }
    
    .sidebar-header h2, 
    .menu-text {
        display: none;
    }
    
    .menu-item {
        justify-content: center;
        padding: 15px 10px;
    }
    
    .menu-item i {
        margin-right: 0;
        font-size: 20px;
    }
}
