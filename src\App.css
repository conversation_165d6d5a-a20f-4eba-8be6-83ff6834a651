/* Global CSS Variables */
:root {
  --primary-color: #2e7d32;
  --primary-light: #4caf50;
  --primary-dark: #1b5e20;
  --secondary-color: #ffd600;
  --secondary-light: #ffee58;
  --secondary-dark: #ffc107;
  --accent-color: #43a047;
  --accent-yellow: #ffeb3b;
  --bg-light: #f8f9fa;
  --text-dark: #1b5e20;
  --text-light: #fff;
  --light-gray: #f5f5f5;
  --medium-gray: #e0e0e0;
  --dark-gray: #424242;
  --sidebar-width: 250px;
  --sidebar-collapsed-width: 60px;
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
  --gradient-yellow: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-yellow) 100%);
  --shadow-sm: 0 2px 8px rgba(46, 125, 50, 0.1);
  --shadow-md: 0 4px 20px rgba(46, 125, 50, 0.08);
  --shadow-lg: 0 8px 32px rgba(46, 125, 50, 0.12);
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --transition: all 0.3s ease;
  --transition-fast: all 0.2s ease;
}

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-light);
  color: var(--text-dark);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.App {
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--primary-color);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.75rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

p {
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  line-height: 1.5;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-light);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: var(--text-light);
  text-decoration: none;
}

.btn-secondary {
  background: var(--gradient-yellow);
  color: var(--text-dark);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: var(--text-dark);
  text-decoration: none;
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--text-light);
  text-decoration: none;
}

/* Cards */
.card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--medium-gray);
}

.card-title {
  color: var(--primary-color);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

/* Forms */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--medium-gray);
  border-radius: var(--border-radius-sm);
  font-size: 1rem;
  transition: var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.form-control.is-valid {
  border-color: #28a745;
}

/* Utilities */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }
.p-5 { padding: 3rem; }

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.container-fluid {
  width: 100%;
  padding: 0 1rem;
}

/* Grid System */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.75rem;
}

.col {
  flex: 1;
  padding: 0 0.75rem;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 0.75rem;
}

.col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 0 0.75rem;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 0 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }
  
  .col-6,
  .col-4,
  .col-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Loading Spinner */
.spinner {
  border: 4px solid var(--medium-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
